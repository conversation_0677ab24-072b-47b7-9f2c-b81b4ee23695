<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS @property 渐变过渡效果演示</title>
    <link rel="stylesheet" href="src/public/component/pay/pay.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .demo-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            max-width: 600px;
            width: 100%;
        }

        .demo-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: 600;
        }

        .demo-description {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            line-height: 1.6;
        }

        .demo-section {
            margin-bottom: 40px;
        }

        .demo-section h3 {
            color: #444;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .demo-button-container {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .demo-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #007bff;
        }

        .demo-info h4 {
            margin-top: 0;
            color: #007bff;
        }

        .demo-info ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        .demo-info li {
            margin-bottom: 8px;
            color: #555;
        }

        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: auto;
            margin: 20px 0;
        }

        .highlight {
            color: #ffd700;
            font-weight: bold;
        }

        .property-demo {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            padding: 20px;
            border-radius: 10px;
            color: white;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px 0;
        }

        .property-demo:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">CSS @property 渐变过渡效果演示</h1>
        <p class="demo-description">
            使用 CSS @property 自定义属性创建平滑的 linear-gradient 渐变过渡效果，
            实现比传统方法更流畅的动画体验。
        </p>

        <div class="demo-section">
            <h3>🎨 按钮渐变过渡效果</h3>
            <div class="demo-button-container">
                <div class="f-pay-btn-box">
                    <div class="f-pay-btn-inner-box">
                        <div class="f-pay-btn-inner-box-text" style="padding: 0 20px; font-weight: 600; color: #333;">
                            悬停查看渐变过渡
                        </div>
                        <div class="f-pay-btn-color-box">
                            <div class="f-pay-btn-color-inner absolute"></div>
                            <div class="f-pay-btn-color-box-text" style="position: relative; z-index: 1; padding: 0 20px; font-weight: 600;">
                                ✨ 效果
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-info">
            <h4>🔧 技术实现要点</h4>
            <ul>
                <li><strong>@property 定义：</strong>为渐变颜色定义可动画的CSS自定义属性</li>
                <li><strong>语法类型：</strong>使用 syntax: '&lt;color&gt;' 确保颜色值可以被正确插值</li>
                <li><strong>平滑过渡：</strong>通过 transition 属性实现自定义属性的平滑变化</li>
                <li><strong>性能优化：</strong>使用 cubic-bezier 缓动函数提供自然的动画效果</li>
            </ul>
        </div>

        <div class="code-snippet">
<span class="highlight">/* 定义可动画的渐变颜色属性 */</span>
@property --gradient-color-1 {
  syntax: '&lt;color&gt;';
  initial-value: #d6e0ff;
  inherits: false;
}

@property --gradient-color-2 {
  syntax: '&lt;color&gt;';
  initial-value: #e3dff8;
  inherits: false;
}

<span class="highlight">/* 使用自定义属性创建渐变 */</span>
.element {
  background: linear-gradient(
    var(--gradient-angle),
    var(--gradient-color-1) var(--gradient-stop-1),
    var(--gradient-color-2) var(--gradient-stop-2)
  );
  <span class="highlight">/* 添加平滑过渡 */</span>
  transition: 
    --gradient-color-1 0.6s cubic-bezier(0.4, 0, 0.2, 1),
    --gradient-color-2 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

<span class="highlight">/* hover状态改变自定义属性值 */</span>
.element:hover {
  --gradient-color-1: #3363ff;
  --gradient-color-2: #775fdd;
}
        </div>

        <div class="demo-info">
            <h4>✨ 优势特点</h4>
            <ul>
                <li><strong>真正的渐变过渡：</strong>不同于传统的opacity或transform，实现真正的颜色渐变动画</li>
                <li><strong>性能优异：</strong>利用浏览器原生优化，避免重绘和重排</li>
                <li><strong>控制精确：</strong>可以独立控制渐变的每个颜色停止点</li>
                <li><strong>兼容性好：</strong>现代浏览器广泛支持@property特性</li>
            </ul>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const button = document.querySelector('.f-pay-btn-box');
            
            button.addEventListener('mouseenter', function() {
                console.log('🎨 渐变过渡开始');
            });
            
            button.addEventListener('mouseleave', function() {
                console.log('🎨 渐变过渡结束');
            });
        });
    </script>
</body>
</html>
