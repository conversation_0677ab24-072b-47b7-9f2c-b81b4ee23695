.pay-wrap {
  background: linear-gradient(180deg, #eaedf6 72%, #eeeaff 100%);
  will-change: transform, opacity;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 直接应用动画效果 */
.dialog-animation {
  animation: dialog-show 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
}

/* 关闭动画效果 */
.dialog-animation-close {
  animation: dialog-close 0.4s cubic-bezier(0.6, -0.28, 0.735, 0.045) forwards;
}

@keyframes dialog-show {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes dialog-close {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* 定义CSS自定义属性用于渐变过渡 */
@property --gradient-color-1 {
  syntax: "<color>";
  initial-value: #d6e0ff;
  inherits: false;
}

@property --gradient-color-2 {
  syntax: "<color>";
  initial-value: #e3dff8;
  inherits: false;
}

@property --gradient-angle {
  syntax: "<angle>";
  initial-value: 92deg;
  inherits: false;
}

@property --gradient-stop-1 {
  syntax: "<percentage>";
  initial-value: 23.41%;
  inherits: false;
}

@property --gradient-stop-2 {
  syntax: "<percentage>";
  initial-value: 100%;
  inherits: false;
}

/* 按钮边框渐变的CSS自定义属性 */
@property --btn-border-color-1 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

@property --btn-border-color-2 {
  syntax: "<color>";
  initial-value: #c3cded;
  inherits: false;
}

@property --btn-border-color-3 {
  syntax: "<color>";
  initial-value: #cbbfff;
  inherits: false;
}

@property --btn-border-color-4 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

/* 内部按钮渐变的CSS自定义属性 */
@property --btn-inner-color-1 {
  syntax: "<color>";
  initial-value: #f8f8f8;
  inherits: false;
}

@property --btn-inner-color-2 {
  syntax: "<color>";
  initial-value: #fff;
  inherits: false;
}

@property --btn-inner-stop-1 {
  syntax: "<percentage>";
  initial-value: 0%;
  inherits: false;
}

@property --btn-inner-stop-2 {
  syntax: "<percentage>";
  initial-value: 63.94%;
  inherits: false;
}

/* 列表项逐个出现的动画 */
.item-animation {
  opacity: 0;
  transform: translateY(40px);
  animation: item-fade-in 0.5s ease forwards;
}

@keyframes item-fade-in {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.f-pay-vl-box {
  width: 352px;
  height: calc(100% - 40px);
}

/* 添加平滑滚动效果 */
.f-pay-scroll-box {
  /* scroll-behavior: smooth; */
  /* iOS平滑滚动 */
  /* -webkit-overflow-scrolling: touch;  */
}

/* .f-pay-inner-box {
  box-shadow:
    -50px 0px 40px -30px #fff inset,
    50px 0px 40px -30px #fff inset,
    0px 0px 20px 0px #ebeef7 inset;
} */

.f-pay-money-box {
  background: linear-gradient(180deg, #f0f0f0 0%, #fff 80%);
}

.f-pay-title-1,
.f-pay-title-2 {
  --fv-title-h: 60px;
  --fv-title-shadow-h: 48px;

  position: sticky;
  height: var(--fv-title-h);
}

.f-pay-title-1::before {
  content: "";
  width: 100%;
  position: absolute;
  top: calc(var(--fv-title-h) - 2px);
  left: 0;
  height: var(--fv-title-shadow-h);
  box-shadow:
    -50px 0px 40px -30px #fff inset,
    50px 0px 40px -30px #fff inset,
    0px 20px 30px -20px #ebeef7 inset;
  pointer-events: none;
}

.f-pay-title-2::before {
  content: "";
  width: 100%;
  position: absolute;
  top: calc(0px - var(--fv-title-shadow-h));
  left: 0;
  height: var(--fv-title-shadow-h);
  box-shadow:
    -50px 0px 40px -30px #fff inset,
    50px 0px 40px -30px #fff inset,
    0px -20px 30px -20px #ebeef7 inset;
  z-index: -1;
  opacity: 1;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

/* 当f-pay-title-2处于sticky状态时，隐藏其::before伪元素 */
.f-pay-title-2.is-sticky::before {
  opacity: 0;
}

.f-pay-title-1::after,
.f-pay-title-2::after {
  content: "";
  width: 100%;
  position: absolute;
  top: var(--fv-title-h);
  left: 0;
  height: var(--fv-title-shadow-h);
  box-shadow:
    -50px 0px 40px -30px #fff inset,
    50px 0px 40px -30px #fff inset,
    0px 20px 30px -20px #ebeef7 inset;
  z-index: -1;
  pointer-events: none;
}

.f-pay-title-2 {
  top: var(--fv-title-h);
  bottom: 0;
}

.f-pay-vl-box-item-animation {
  opacity: 0;
  transform: translateX(100px);
  animation: vl-box-item-fade-in 0.5s ease forwards;
}

@keyframes vl-box-item-fade-in {
  0% {
    opacity: 0;
    transform: translateX(100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.f-pay-btn-box {
  --fv-border: 2px;
  --fv-radius: 12px;

  padding: var(--fv-border);
  background: linear-gradient(
    180deg,
    var(--btn-border-color-1) 0%,
    var(--btn-border-color-2) 30%,
    var(--btn-border-color-3) 70%,
    var(--btn-border-color-4) 100%
  );
  border-radius: var(--fv-radius);
  box-shadow:
    0px -6px 10px -10px rgba(0, 0, 0, 0.25),
    0px 14px 15px -10px #d8def0;
  /* 添加平滑过渡效果 */
  transition:
    --btn-border-color-1 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-border-color-2 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-border-color-3 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-border-color-4 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.3s ease,
    transform 0.2s ease;
}

.f-pay-btn-box:hover {
  /* 使用CSS自定义属性实现平滑渐变过渡 */
  --btn-border-color-1: #fff;
  --btn-border-color-2: #8ca0dc;
  --btn-border-color-3: #947ef4;
  --btn-border-color-4: #fff;

  box-shadow:
    0px -6px 10px -10px #385cffff,
    0px 14px 30px -15px #7b9cff;
}

.f-pay-btn-box:active {
  transform: translateY(2px);
    box-shadow:
    0px -3px 8px -10px #385cffff,
    0px 10px 20px -15px #7b9cff;
}

.f-pay-btn-box .f-pay-btn-inner-box {
  --fv-height: 51px;

  overflow: hidden;
  height: var(--fv-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(
    90deg,
    var(--btn-inner-color-1) var(--btn-inner-stop-1),
    var(--btn-inner-color-2) var(--btn-inner-stop-2)
  );
  border-radius: calc(var(--fv-radius) - var(--fv-border));
  cursor: pointer;
  /* 添加平滑过渡效果 */
  transition:
    --btn-inner-color-1 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-inner-color-2 0.4s cubic-bezier(0.4, 0, 0.2, 1),
    --btn-inner-stop-1 0.3s ease,
    --btn-inner-stop-2 0.3s ease,
    transform 0.2s ease;
}

.f-pay-btn-box:hover .f-pay-btn-inner-box {
  /* 使用CSS自定义属性实现平滑渐变过渡 */
  --btn-inner-color-1: #e9edfa;
  --btn-inner-color-2: #ebebfa;
  --btn-inner-stop-1: 0%;
  --btn-inner-stop-2: 69.71%;
}

.f-pay-btn-box:hover .f-pay-btn-inner-box-text {
  text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
}

.f-pay-btn-box .f-pay-btn-inner-box .f-pay-btn-color-box {
  --fv-width: 128px;
  width: var(--fv-width);
  height: var(--fv-height);
}

.f-pay-btn-color-box-text {
  text-shadow:
    -1px -1px 2px #fff,
    1px 1px 2px rgba(12, 0, 105, 0.1);
  transition: all 0.4s ease-out;
}

.f-pay-btn-box:hover .f-pay-btn-color-box-text {
  color: #fff;
  text-shadow: 1px 1px 2px rgba(51, 99, 255, 0.66);
}

.f-pay-btn-color-box .f-pay-btn-color-inner {
  width: var(--fv-width);
  height: var(--fv-height);
  border-radius: 100px;
  background: linear-gradient(
    var(--gradient-angle),
    var(--gradient-color-1) var(--gradient-stop-1),
    var(--gradient-color-2) var(--gradient-stop-2)
  );
  filter: blur(15px);
  z-index: -1;
  top: 0px;
  /* 添加平滑过渡效果 */
  transition:
    --gradient-color-1 0.6s cubic-bezier(0.4, 0, 0.2, 1),
    --gradient-color-2 0.6s cubic-bezier(0.4, 0, 0.2, 1),
    --gradient-angle 0.4s ease-out,
    --gradient-stop-1 0.5s ease-in-out,
    --gradient-stop-2 0.5s ease-in-out,
    filter 0.3s ease,
    transform 0.3s ease;
}

.f-pay-btn-box:hover .f-pay-btn-color-box .f-pay-btn-color-inner {
  --fv-top: 3px;

  /* 使用CSS自定义属性实现平滑渐变过渡 */
  --gradient-color-1: #3363ff;
  --gradient-color-2: #775fdd;
  --gradient-angle: 95deg;
  --gradient-stop-1: 0%;
  --gradient-stop-2: 100%;

  height: calc(var(--fv-height) + 2 * var(--fv-top));
  top: -var(--fv-top);
}

.f-pay-btn-box:active .f-pay-btn-color-box .f-pay-btn-color-inner {
  width: calc(var(--fv-width) - 10px);
}