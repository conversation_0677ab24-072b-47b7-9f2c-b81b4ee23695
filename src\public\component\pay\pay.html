<div>
  <link rel="stylesheet" href="component/pay/pay.css" />
  <div
    class="pay-wrap dialog-animation z-100 absolute left-0 top-0 flex h-full w-full justify-center space-x-4"
    v-show="cz_qudan"
    :style="{ opacity: 0 }"
  >
    <div
      class="f-pay-vl-box-item-animation rounded-100 hover:shadow-base center mt-8 h-10 w-10 cursor-pointer bg-white text-xl shadow-lg transition hover:opacity-70 active:mt-9"
      @click="cz_qudan_close"
      style="animation-delay: 100ms; opacity: 0"
    >
      <i class="el-icon-arrow-left"></i>
    </div>

    <div
      class="f-pay-vl-box f-pay-vl-box-item-animation shadow-base hover:shadow-baseh mt-2 flex shrink-0 flex-col justify-between overflow-hidden rounded-lg bg-white transition"
      style="animation-delay: 200ms; opacity: 0"
    >
      <div class="f-pay-scroll-box scrollbar-hide relative overflow-y-auto">
        <div
          class="f-pay-title-1 group top-0 z-10 flex shrink-0 cursor-pointer items-center justify-between bg-white px-6 text-lg font-bold"
          @click="handleScrollToTitle_1"
        >
          <div class="o-font-shadow">消费明细</div>
          <ficon
            icon="MaterialSymbolsExpandAll"
            class="text-gray text-sm opacity-0 transition-all group-hover:opacity-100"
          ></ficon>
        </div>
        <div
          class="f-pay-inner-box scrollbar-hide z-1 box-border w-full overflow-y-auto px-8 py-6 text-sm transition-all"
        >
          <el-skeleton
            class="w-full space-y-4"
            :loading="orderListLoading"
            animated
          >
            <template slot="template">
              <div class="flex space-x-2" v-for="i in 2" :key="i">
                <el-skeleton-item variant="text" style="width: 20px" />
                <div class="flex-1 space-y-3 pb-3">
                  <div class="space-y-1">
                    <el-skeleton-item variant="text" />
                    <el-skeleton-item variant="text" />
                    <el-skeleton-item variant="text" />
                  </div>
                  <el-divider></el-divider>
                </div>
              </div>
            </template>
            <template>
              <div
                class="item-animation flex w-full space-x-1"
                v-for="(item,index) in orderDetails.orderInfo"
                :style="{ animationDelay: index * 100 + 'ms' }"
                :key="'orderInfo'+index"
              >
                <div
                  class="text-primary w-5 shrink-0 font-bold"
                  style="margin-top: -2px"
                >
                  {{index+1}}.
                </div>
                <div class="flex-1 space-y-2 leading-4">
                  <div class="flex justify-between space-x-4">
                    <div>{{item.name}}</div>
                    <div class="shrink-0">￥{{item.price}}</div>
                  </div>
                  <div class="flex justify-between space-x-4">
                    <div>数量</div>
                    <div class="shrink-0">×{{item.num}}</div>
                  </div>
                  <!-- <div class="order_info_li" v-if="item.reduceprice">
                        <span v-if="item.equity_type==2">折扣</span>
                        <span v-if="item.equity_type==3">抵扣</span>
                        <span v-if="item.equity_type==4">优惠金额</span>
                        <span>-￥{{item.reduceprice | filterMoney}}</span>
                    </div> -->
                  <div
                    class="flex justify-between space-x-4"
                    v-if="item.equity_type!=1"
                  >
                    <div v-if="item.equity_type==3">次卡抵扣</div>
                    <div v-if="item.equity_type==2">充值卡折扣</div>
                    <div class="shrink-0" v-if="item.equity_type!=4">
                      -￥{{item.reduceprice | formatMark}}
                    </div>
                  </div>
                  <div
                    class="flex justify-between space-x-4"
                    v-if="item.equity_type==4"
                  >
                    <div v-if="item.equity_type==4">优惠金额</div>
                    <div
                      v-if="item.reduceprice && item.reduceprice.indexOf('-')==-1"
                    >
                      +￥{{item.reduceprice | formatMark}}
                    </div>
                    <div
                      class="shrink-0"
                      v-if="item.reduceprice && item.reduceprice.indexOf('-')!=-1"
                    >
                      -￥{{item.reduceprice | formatMark}}
                    </div>
                  </div>
                  <div
                    class="flex justify-between space-x-4"
                    style="margin-bottom: 1rem"
                  >
                    <div>小计</div>
                    <div class="shrink-0">￥{{item.Subtotal}}</div>
                  </div>
                  <el-divider></el-divider>
                </div>
              </div>
            </template>
          </el-skeleton>
        </div>

        <template
          v-if="orderDetails.presentData && orderDetails.presentData.length>0"
        >
          <div
            class="f-pay-title-2 group z-10 flex shrink-0 cursor-pointer items-center justify-between bg-white px-6 text-lg font-bold"
            @click="handleScrollToTitle_2"
          >
            <div class="o-font-shadow">赠送明细</div>
            <ficon
              icon="MaterialSymbolsExpandAll"
              class="text-gray text-sm opacity-0 transition-all group-hover:opacity-100"
            ></ficon>
          </div>
          <div class="flex flex-col">
            <div
              class="f-pay-inner-box scrollbar-hide box-border w-full overflow-y-auto px-8 py-6 text-sm transition-all"
            >
              <el-skeleton
                class="w-full space-y-4"
                :loading="orderListLoading"
                animated
              >
                <template slot="template">
                  <div class="flex space-x-2">
                    <el-skeleton-item variant="text" style="width: 20px" />
                    <div class="flex-1 space-y-1">
                      <el-skeleton-item variant="text" />
                      <el-skeleton-item variant="text" />
                      <el-skeleton-item variant="text" />
                    </div>
                  </div>
                </template>
                <template>
                  <div
                    class="item-animation flex w-full space-x-1"
                    v-for="(item,index) in orderDetails.presentData"
                    :style="{ animationDelay: index * 100 + 'ms' }"
                    :key="'presentData'+index"
                  >
                    <div
                      class="text-primary w-5 shrink-0 font-bold"
                      style="margin-top: -2px"
                    >
                      {{index+1}}.
                    </div>
                    <div class="flex-1 space-y-2 leading-4">
                      <div class="flex justify-between space-x-4">
                        <div>
                          <!-- <span>{{item.name}}</span>
                      <span v-if="item.sku_name">| {{item.sku_name}}</span> -->
                          <div v-if="item.itemType==1">
                            {{item.name}}（服务）
                          </div>
                          <div v-if="item.itemType==2 && !item.sku_name">
                            {{item.name}}（产品）
                          </div>
                          <div v-if="item.itemType==2 && item.sku_name">
                            {{item.name}}
                          </div>
                          <div v-if="item.sku_name">
                            | {{item.sku_name}}（产品）
                          </div>
                        </div>
                        <div class="shrink-0">
                          ￥{{filterMoney(item.price)}}
                        </div>
                      </div>
                      <div class="flex justify-between space-x-4">
                        <div>数量</div>
                        <div class="shrink-0">×{{item.num}}</div>
                      </div>
                      <div
                        class="flex justify-between space-x-4"
                        style="margin-bottom: 1rem"
                      >
                        <div>赠送状态</div>
                        <div class="shrink-0">
                          <span class="order-label2" v-if="item.status==0"
                            >仅选择</span
                          >
                          <span class="order-label2" v-if="item.status==1"
                            >已赠送</span
                          >
                          <span class="order-label2" v-if="item.status==2"
                            >已退回</span
                          >
                        </div>
                      </div>
                      <el-divider></el-divider>
                    </div>
                  </div>
                </template>
              </el-skeleton>
            </div>
          </div>
        </template>
      </div>

      <div class="f-pay-money-box shrink-0 space-y-3 px-6 pb-4 pt-6">
        <el-skeleton
          class="w-full space-y-1 text-sm"
          :loading="orderListLoading"
          animated
        >
          <template slot="template">
            <el-skeleton-item variant="text" />
          </template>
          <template>
            <div class="flex justify-between space-x-4">
              <div>合计</div>
              <div class="shrink-0">￥{{filterMoney(receivableing)}}</div>
            </div>
            <div
              class="flex justify-between space-x-4"
              v-if="kd_xinxi_list?.member_counpon_money!=undefined && kd_xinxi_list?.member_counpon_money!=0 && kd_xinxi_list?.member_coupon!=0"
            >
              <div>优惠</div>
              <div class="shrink-0">
                -￥{{filterMoney(kd_xinxi_list.member_counpon_money)}}
              </div>
            </div>
            <div
              class="flex justify-between space-x-4"
              v-if="kd_xinxi_list.dismoney"
            >
              <div>充值卡折扣</div>
              <div class="shrink-0" v-if="kd_xinxi_list.dismoney">
                -￥{{filterMoney(kd_xinxi_list.dismoney)}}
              </div>
            </div>
            <div
              class="flex justify-between space-x-4"
              v-if="kd_xinxi_list.deduction"
            >
              <div>次卡抵扣</div>
              <div class="shrink-0" v-if="kd_xinxi_list.deduction">
                -￥{{filterMoney(kd_xinxi_list.deduction)}}
              </div>
            </div>
            <div
              class="flex justify-between space-x-4"
              v-if="kd_xinxi_list.manually"
            >
              <div>优惠金额</div>
              <div class="shrink-0" v-if="kd_xinxi_list.manually>0">
                -￥{{orderDetails.manuallys}}
              </div>
              <div class="shrink-0" v-else>+￥{{orderDetails.manuallys}}</div>
            </div>
            <div
              class="flex justify-between space-x-4"
              v-if="orderDetails.small_change_money"
            >
              <div>抹零</div>
              <div class="shrink-0" v-if="orderDetails.small_change_money">
                -￥{{filterMoney(orderDetails.small_change_money)}}
              </div>
            </div>
            <div
              class="flex justify-between space-x-4"
              v-if="kd_xinxi_list.net_receipts>0"
            >
              <div>预约定金</div>
              <div class="shrink-0" v-if="kd_xinxi_list.net_receipts">
                ￥{{filterMoney(kd_xinxi_list.net_receipts)}}
              </div>
            </div>
            <!-- <div class="order_info_li" v-if="isDebtFlag">
                    <span>合计</span>
                    <span>￥{{orderDetails.receivable}}</span>
                </div> -->
            <div class="flex justify-between space-x-4" v-if="isDebtFlag">
              <div>已付款</div>
              <div class="shrink-0">
                -￥{{filterMoney(orderDetails.alreadyPay)}}
              </div>
            </div>
            <div class="flex justify-between space-x-4" v-if="isDebtMoney">
              <div>欠款</div>
              <div class="shrink-0">￥{{filterMoney(debtForm.debtMoney)}}</div>
            </div>
          </template>
        </el-skeleton>
        <el-divider></el-divider>
        <el-skeleton
          class="flex w-full items-center justify-between font-bold"
          :loading="getOrderDetailsLoading"
          animated
        >
          <template slot="template">
            <el-skeleton-item variant="h3" />
          </template>
          <template>
            <div class="o-font-shadow text-lg">
              <span v-show="endPay==0">待支付</span>
              <span v-show="endPay==1">已支付</span>
            </div>
            <div class="o-font-shadow flex items-baseline" v-if="!isDebtMoney">
              <div class="pr-1">￥</div>
              <div class="text-2xl">
                {{filterMoney(kd_xinxi_list.toBePay).split('.')[0]}}
              </div>
              <div class="text-lg">
                .{{filterMoney(kd_xinxi_list.toBePay).split('.')[1]}}
              </div>
            </div>
            <div class="o-font-shadow flex items-baseline" v-if="isDebtMoney">
              <div class="pr-1">￥</div>
              <div class="text-2xl">
                {{filterMoney(debtForm.payMoney || 0.00).split('.')[0]}}
              </div>
              <div class="text-lg">
                .{{filterMoney(debtForm.payMoney || 0.00).split('.')[1]}}
              </div>
            </div>
          </template>
        </el-skeleton>
      </div>
    </div>

    <div
      style="animation-delay: 300ms; opacity: 0"
      class="f-pay-vl-box f-pay-vl-box-item-animation mt-2 flex flex-col space-y-4 transition-all"
    >
      <div
        class="shadow-base hover:shadow-baseh flex shrink-0 flex-col overflow-hidden rounded-lg bg-white pb-4 transition"
      >
        <div
          class="f-pay-title-1 o-font-shadow group top-0 z-10 flex shrink-0 items-center bg-white px-6 text-lg font-bold"
        >
          订单信息
        </div>

        <el-skeleton
          class="relative z-10 space-y-2 px-6 pb-4 pt-5 text-sm"
          :loading="orderListLoading"
          animated
        >
          <template slot="template">
            <div class="space-y-2">
              <el-skeleton-item v-for="i in 5" :key="'l2'+i" variant="text" />
            </div>
          </template>
          <template>
            <div class="item-animation flex items-center justify-between">
              <div class="shrink-0">订单编号</div>
              <div>{{kd_xinxi_list.order_number}}</div>
            </div>
            <div
              class="item-animation flex items-center justify-between"
              v-if="endPay==1"
            >
              <div class="shrink-0">下单时间</div>
              <div>
                {{manualOrderTime?manualOrderTime:kd_xinxi_list.order_time}}
              </div>
            </div>
            <div
              class="item-animation flex items-center justify-between"
              style="height: 33px; animation-delay: 100ms"
              v-else
              @click="handleModifyOrderTime"
            >
              <div
                class="text-primary hover:text-primary/70 flex shrink-0 cursor-pointer items-center space-x-2"
              >
                <div>下单时间</div>
                <i class="el-icon-edit"></i>
              </div>
              <div v-if="!manualOrderTime && !isEditOrderTime">
                {{kd_xinxi_list.order_time}}
              </div>
              <el-date-picker
                size="small"
                v-if="isEditOrderTime"
                v-model="manualOrderTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期时间"
                style="width: 200px"
              ></el-date-picker>
            </div>
            <div
              class="item-animation flex items-center justify-between"
              v-if="kd_xinxi_list_buyer && kd_xinxi_list_buyer.id!=0"
              style="animation-delay: 250ms"
            >
              <div class="shrink-0">会员编号</div>
              <div>{{kd_xinxi_list?.buyer?.member_number}}</div>
            </div>
            <div
              class="item-animation flex items-center justify-between"
              v-if="kd_xinxi_list_buyer && kd_xinxi_list_buyer.id!=0"
              style="animation-delay: 300ms"
            >
              <div class="shrink-0">下单人</div>
              <div v-if="kd_xinxi_list_buyer && kd_xinxi_list_buyer.id!=0">
                {{kd_xinxi_list_buyer.member_name}}
              </div>
              <div v-else>普通顾客</div>
            </div>
            <div
              class="item-animation flex items-center justify-between"
              style="animation-delay: 350ms"
            >
              <div class="shrink-0">收银员</div>
              <div>{{kd_xinxi_list_cashierInfo.nickname}}</div>
            </div>
          </template>
        </el-skeleton>
      </div>
      <div
        class="shadow-base hover:shadow-baseh flex flex-1 flex-col overflow-hidden rounded-lg bg-white transition"
      >
        <div
          class="f-pay-title-1 o-font-shadow group top-0 z-10 flex shrink-0 items-center bg-white px-6 text-lg font-bold"
        >
          收银方式
        </div>
        <div
          class="scrollbar-hide relative z-10 h-0 flex-1 space-y-4 overflow-y-auto px-3 py-4"
        >
          <f-pay-btn
            v-if="isConsumeCards"
            title="卡项抵扣"
            @click="custmizePayTypeBill"
          />
          <template v-else>
            <f-pay-btn
              v-if="kd_xinxi_list_buyer && kd_xinxi_list_buyer.id!=0 && payCardInfo.length>0"
              title="会员余额"
              @click="bindPay(0)"
            />
            <f-pay-btn
              v-for="(store,index) of customizePayTypeList"
              :key="'pbt'+index"
              :title="store.name"
              @click="chooseCustmizePayType(store.payType)"
            />
            <f-pay-btn title="现金" @click="bindPay(2)" />
          </template>
        </div>
      </div>
    </div>
  </div>
</div>
