.uuai_title {
  width: 100%;
  height: 30px;
  box-sizing: border-box;
  font-size: 18px;
  padding: 5px 0;
}

/* SVG图标组件样式 */
.svg-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  line-height: 1;
}

.svg-icon svg {
  width: 1em;
  height: 1em;
}

/* 预设颜色类 */
.text-red svg {
  color: #f56c6c;
}

.text-blue svg {
  color: #409eff;
}

.text-green svg {
  color: #67c23a;
}

.text-orange svg {
  color: #e6a23c;
}

/* 预设大小类 */
.text-sm svg {
  font-size: 0.875em;
}

.text-md svg {
  font-size: 1em;
}

.text-lg svg {
  font-size: 1.25em;
}

.text-xl svg {
  font-size: 1.5em;
}

.text-2xl svg {
  font-size: 2em;
}

.caption {
  border-left: 4px solid #3363FF;
  padding: 0 15px;
}

/* 返回 */

.return-container {
  height: 50px;
  line-height: 50px;
  background: #f6f6f6;
  box-sizing: border-box;
  padding: 0 15px;
  text-align: center;
}

.return-title {
  display: flex;
}

.return-text {
  width: 100px;
  cursor: pointer;
  color: #999;
}

.details-text {
  width: calc(100vw - 200px);
  font-size: 18px;
  font-weight: 400;
}

.title-label {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  margin: 15px 0;
}

.text {
  display: inline-block;
  width: 100px;
  text-align: center;
  font-size: 16px;
}

.title-label:after,
.title-label:before {
  display: inline-block;
  content: "";
  width: 45%;
  height: 1px;
  background: #e5e5e5;
}

.title-label:before {
  padding-left: 15px;
}

.title-label:after {
  padding-right: 15px;
}

/*添加服务*/
.add-server-mask .el-dialog__body {
  padding: 30px 0;
}

.xuanze_jishi_search {
  box-sizing: border-box;
  padding: 0 20px;
  margin-bottom: 20px;
}

.tianjia_fuwu {
  font-size: 22px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  text-align: center;
}

.tianjia_fuwu_search {
  border: 1px solid rgba(221, 221, 221, 1);
  border-radius: 4px;
  display: flex;
  justify-content: space-between;
  padding: 10px 5px 10px 5px;
  margin-top: 10px;
}

.tianjia_fuwu_input {
  border: 0;
  outline: none;
  background: 0;
}

.tianjia_fuwu_mian {
  display: flex;
  overflow: hidden;
  height: 300px;
  margin-bottom: 30px;
}

.fuwu_biaoti {
  width: 20%;
  height: 100%;
  overflow-y: auto;
  border-right: 1px solid rgba(221, 221, 221, 1);
}

.fuwu_biaoti_chioce {
  width: 80%;
  height: 100%;
  overflow-y: auto;
}

.tianjia_fuwu_font {
  box-sizing: border-box;
  font-size: 14px;
  color: rgba(102, 102, 102, 1);
  padding: 20px 15px;
  border-bottom: 1px solid rgba(221, 221, 221, 1);
  cursor: pointer;
}

.serverActive {
  background: #ebdcf2;
  color: #3363FF;
}

.fuwu_biaoti_chioce_bottom {
  border-bottom: 0.5px solid rgba(221, 221, 221, 1);
}

.server_biaoti_name_font {
  box-sizing: border-box;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.server_biaoti_name_font1 {
  width: 60px;
  height: 60px;
  display: block;
  margin-right: 10px;
}

.server_biaoti_name_font1 > img {
  display: inline-block;
  width: 100%;
  height: 100%;
}

.add-server-shop {
  flex: 1;
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
}

.server_biaoti_name_font2 {
  width: 150px;
  font-size: 16px;
  font-weight: 400;
  color: rgba(51, 51, 51, 1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.server_biaoti_name_font3 {
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
  text-align: right;
  margin-right: 10px;
}

.cancelPrint {
  text-decoration: line-through;
}

.itemPrint {
  margin-bottom: 8px;
}

.server-footer {
  box-sizing: border-box;
  padding: 0 20px;
  text-align: right;
}

.fuwu_biaoti::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.fuwu_biaoti::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.fuwu_biaoti::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.cancel_server.el-button:hover,
.cancel_server.el-button:active,
.cancel_server.el-button:focus {
  color: #606266;
  border-color: #dcdfe6;
  background-color: transparent;
}

/*预约 and 订单--详情*/
.dialog-title {
  display: flex;
  justify-content: space-between;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 20px;
  border-bottom: 1px solid #f6f6f6;
}

.notice-mask > .el-dialog__header {
  padding: 0;
}

.dialog-title > .el-dialog__close,
.cancelReservation {
  display: inline-block;
  width: 80px;
  cursor: pointer;
}

.notice-mask > .el-dialog__body {
  padding: 0;
}

.cancelReservation {
  color: #3363FF;
  text-align: center;
  font-size: 14px;
}

.detailsTable-ul {
  display: flex;
  box-sizing: border-box;
  padding: 10px;
  border-bottom: 1px solid #f6f6f6;
}

.detailsTable-li {
  flex: 1;
  box-sizing: border-box;
  padding: 0 15px;
}

.shoperName {
  width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shoper-tip {
  display: inline-block;
  box-sizing: border-box;
  padding: 4px 6px;
  background: #f6f6f6;
  border-radius: 4px;
  font-size: 12px;
}

.margin-bottom {
  margin-bottom: 5px;
}

.arrdate {
  width: 150px !important;
}

.arrdate > .el-input__inner {
  cursor: pointer;
  padding: 0 5px;
  color: #3363FF;
}

.details-server {
  box-sizing: border-box;
  /*padding: 0 20px;*/
  height: 400px;
  overflow-y: auto;
  background: #f6f6f6;
}

.details-wrap {
  display: flex;
  border-bottom: 1px solid #f6f6f6;
  box-sizing: border-box;
  padding: 15px 20px;
  position: relative;
  background: #fff;
}

.details-label {
  width: 60px;
}

.details-server-wrap {
  flex: 1;
}

.details-server-info {
  width: 100%;
  margin-bottom: 15px;
}

.details-product {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.details-product-list {
  margin-bottom: 10px;
}

.details-product-list:last-child {
  margin-bottom: 0;
}

.details-technician {
  display: flex;
  justify-content: space-between;
}

.details-order-status {
  position: absolute;
  top: 15px;
  right: 20px;
}

.seeDetails {
  font-size: 14px;
  color: #3363FF;
}

.notice-mask .dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-align: left;
  box-sizing: border-box;
  padding: 5px 0px 0px 15px;
}

.reservation-price {
  color: #333;
  margin-bottom: 5px;
}

.reservation-all {
  color: #999;
  font-size: 12px;
}

.reservation-group {
  display: flex;
}

.reservationBtn {
  padding: 15px 35px;
  color: #fff;
  text-align: center;
  font-size: 15px;
  cursor: pointer;
}

.save-btn {
  background: #dddddd;
}

.billing-btn {
  background: #3363FF;
}

.appCancel > .dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 40px;
}

.cancelDefault:focus,
.cancelDefault:hover {
  color: #333;
  border-color: #dcdfe6;
  background-color: #fff;
}

.changeActive {
  color: #3363FF;
  cursor: pointer;
}

/*订单详情*/

.order-content {
  display: flex;
  width: 100%;
  height: calc(100vh - 110px);
  box-sizing: border-box;
}

.order-details {
  height: 100vh;
  overflow: hidden;
}

.order-details .el-dialog__header {
  padding: 0;
}

.order-details .el-dialog__body {
  padding: 0;
}

.contner-userInfo {
  width: 40%;
  border-right: 3px solid #f6f6f6;
  box-sizing: border-box;
  padding: 0 20px;
}

.order-status {
  text-align: center;
  font-size: 18px;
  color: #3363FF;
}

.status-li {
  height: 30px;
  line-height: 30px;
  margin-bottom: 10px;
}

.order-date {
}

.order-date-li {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.order-date-li .order-label {
  /* min-width: 120px; */
}

.order-label {
  color: #999999;
}

.order-label2 {
  color: #666;
}

.details-vipImg {
  width: 50px;
  height: 50px;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  margin: 0 auto;
  margin-bottom: 10px;
}

.details-vipImg > img {
  display: inline-block;
  width: 100%;
  height: 100%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
}

.client-info {
  text-align: center;
}

.client-info > .icontouxiang {
  display: inline-block;
  font-size: 50px;
  color: #999;
  margin-bottom: 10px;
}

.client-name {
  margin-bottom: 10px;
}

.client-phone {
  color: #999;
  font-size: 14px;
  margin-bottom: 10px;
}

.content-product {
  width: 60%;
  /* return-title details-btn*/
  height: calc(100vh - 120px);
  overflow-y: auto;
  box-sizing: border-box;
  padding-left: 20px;
  font-size: 15px;
  position: relative;
}

.product-list {
  /*50 + 46 + 230 + 57*/
  /* max-height: calc(100vh - 440px); */
  /* overflow-y: auto; */
  box-sizing: border-box;
  padding-right: 20px;
  margin-bottom: 15px;
}

.content-product::-webkit-scrollbar,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  width: 8px;
  overflow: hidden;
}

.content-product::-webkit-scrollbar-thumb,
.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面小方块*/
  width: 8px;
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.content-product::-webkit-scrollbar-track.fuwu_biaoti_chioce::-webkit-scrollbar {
  /*滚动条里面轨道*/
  -webkit-box-shadow: inset 0 0 5px #eee;
  border-radius: 0;
  background: #fff;
}

.product-userInfo {
  box-sizing: border-box;
  padding-right: 20px;
}

.orderDetails-li {
  display: flex;
  box-sizing: border-box;
  padding: 12px 0 0 0;
  border-bottom: 1px solid #e5e5e5;
}

.serialNumber {
  width: 30px;
  color: #999;
}

.details-list {
  width: calc(100% - 30px);
}

.details-btn {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding-left: 20px;
  border-top: 2px solid #f6f6f6;
}

.orderBtn {
  box-sizing: border-box;
  padding: 20px 40px;
}

.print {
  background: #dddddd;
}

.change {
  background: #3363FF;
  color: #fff;
}
